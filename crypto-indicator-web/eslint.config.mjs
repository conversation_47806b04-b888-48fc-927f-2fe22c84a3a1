import antfu from '@antfu/eslint-config'

export default antfu(
  {
    // Type of the project
    type: 'app',
    
    // Enable React and TypeScript support
    react: true,
    typescript: {
      tsconfigPath: 'tsconfig.json',
    },
    
    // Disable backend frameworks
    vue: false,
    svelte: false,
    astro: false,
    solid: false,
    node: false,
    
    // Disable formatters (we'll use <PERSON><PERSON><PERSON> separately)
    formatters: false,
    
    // Configure stylistic rules
    stylistic: {
      indent: 2,
      quotes: 'single',
      semi: true,
    },
    
    // Ignore patterns
    ignores: [
      'build/',
      'node_modules/',
      'public/',
      '**/*.test.ts',
      '**/*.test.tsx',
      '**/*.spec.ts',
      '**/*.spec.tsx',
      'src/generated/**/*',
    ],
  },
  
  // Custom rules for React and financial applications
  {
    files: ['**/*.ts', '**/*.tsx'],
    rules: {
      // TypeScript strict rules for financial applications
      '@typescript-eslint/no-explicit-any': 'error',
      '@typescript-eslint/prefer-nullish-coalescing': 'error',
      '@typescript-eslint/prefer-optional-chain': 'error',
      '@typescript-eslint/no-floating-promises': 'error',
      '@typescript-eslint/await-thenable': 'error',
      '@typescript-eslint/no-misused-promises': 'error',
      '@typescript-eslint/require-await': 'error',
      '@typescript-eslint/no-unnecessary-type-assertion': 'error',
      '@typescript-eslint/prefer-as-const': 'error',
      '@typescript-eslint/switch-exhaustiveness-check': 'error',
      
      // React-specific rules
      'react/react-in-jsx-scope': 'off', // Not needed with React 17+
      'react/prop-types': 'off', // Using TypeScript for prop validation
      'react/display-name': 'error',
      'react/no-array-index-key': 'warn',
      'react/no-danger': 'error',
      'react/jsx-key': 'error',
      'react/jsx-no-duplicate-props': 'error',
      'react/jsx-no-undef': 'error',
      'react/jsx-uses-react': 'off', // Not needed with React 17+
      'react/jsx-uses-vars': 'error',
      'react/no-deprecated': 'error',
      'react/no-direct-mutation-state': 'error',
      'react/no-is-mounted': 'error',
      'react/no-render-return-value': 'error',
      'react/no-string-refs': 'error',
      'react/no-unescaped-entities': 'error',
      'react/no-unknown-property': 'error',
      'react/no-unsafe': 'error',
      'react/require-render-return': 'error',
      'react/self-closing-comp': 'error',
      'react/void-dom-elements-no-children': 'error',
      
      // React Hooks rules
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',
      
      // Code quality and maintainability (slightly relaxed for React)
      'complexity': ['error', { max: 15 }],
      'max-depth': ['error', 4],
      'max-lines': ['warn', { max: 300, skipBlankLines: true, skipComments: true }],
      'max-lines-per-function': ['warn', { max: 80, skipBlankLines: true, skipComments: true }],
      'max-params': ['error', 5], // React components can have more props
      
      // Security rules for financial applications
      'no-eval': 'error',
      'no-implied-eval': 'error',
      'no-new-func': 'error',
      'no-script-url': 'error',
      
      // Performance and best practices
      'prefer-const': 'error',
      'no-var': 'error',
      'prefer-arrow-callback': 'error',
      'prefer-template': 'error',
      'no-nested-ternary': 'error',
      'no-unneeded-ternary': 'error',
      'no-else-return': 'error',
      'consistent-return': 'off', // React components don't always need consistent returns
      'no-return-assign': 'error',
      
      // Allow void for fire-and-forget promises
      'no-void': ['error', { allowAsStatement: true }],
      
      // Allow magic numbers for common React values
      'no-magic-numbers': [
        'warn',
        {
          ignore: [-1, 0, 1, 2, 100, 1000],
          ignoreArrayIndexes: true,
          ignoreDefaultValues: true,
          ignoreClassFieldInitialValues: true,
        },
      ],
      
      // Disable some opinionated rules for React
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
    },
  },
  
  // Test files have relaxed rules
  {
    files: ['**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      'no-magic-numbers': 'off',
      'max-lines-per-function': 'off',
      'react/display-name': 'off',
    },
  },
  
  // Generated files have minimal rules
  {
    files: ['src/generated/**/*'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      'unused-imports/no-unused-imports': 'off',
      'perfectionist/sort-named-imports': 'off',
    },
  },
)
