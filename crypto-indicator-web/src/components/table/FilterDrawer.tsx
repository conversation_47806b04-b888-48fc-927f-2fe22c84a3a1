import React, { useEffect } from "react";
import type { FilterConfig } from "../../types/table";

interface FilterDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  filterConfig: FilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
}

export const FilterDrawer: React.FC<FilterDrawerProps> = ({
  isOpen,
  onClose,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
}) => {
  // Handle escape key and backdrop click
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll but allow drawer content to scroll
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.width = "100%";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
    };
  }, [isOpen, onClose]);

  // Handle touch gestures for mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    const startY = touch.clientY;

    const handleTouchMove = (moveEvent: TouchEvent) => {
      const currentTouch = moveEvent.touches[0];
      const deltaY = currentTouch.clientY - startY;

      // Close drawer if swiped down significantly
      if (deltaY > 100) {
        onClose();
        document.removeEventListener("touchmove", handleTouchMove);
      }
    };

    const handleTouchEnd = () => {
      document.removeEventListener("touchmove", handleTouchMove);
      document.removeEventListener("touchend", handleTouchEnd);
    };

    document.addEventListener("touchmove", handleTouchMove, { passive: false });
    document.addEventListener("touchend", handleTouchEnd);
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="filter-drawer-backdrop" onClick={onClose} />
      <div className="filter-drawer" onTouchStart={handleTouchStart}>
        <div className="filter-drawer-header">
          <h3>Filter Cryptocurrencies</h3>
          <button className="filter-drawer-close" onClick={onClose}>
            ✕
          </button>
        </div>

        <div className="filter-drawer-content">
          <div className="filter-section">
            <label htmlFor="mobile-usd-signal">USD Signal</label>
            <select
              id="mobile-usd-signal"
              value={filterConfig.usdSignal}
              onChange={(e) =>
                onUsdSignalChange(e.target.value as FilterConfig["usdSignal"])
              }
              className="filter-drawer-select"
            >
              <option value="all">All Signals</option>
              <option value="gold">🟡 Gold (Bullish)</option>
              <option value="blue">🔵 Blue (Bearish)</option>
              <option value="gray">⚪ Gray (Neutral)</option>
            </select>
          </div>

          <div className="filter-section">
            <label htmlFor="mobile-btc-signal">BTC Signal</label>
            <select
              id="mobile-btc-signal"
              value={filterConfig.btcSignal}
              onChange={(e) =>
                onBtcSignalChange(e.target.value as FilterConfig["btcSignal"])
              }
              className="filter-drawer-select"
            >
              <option value="all">All Signals</option>
              <option value="gold">🟡 Gold (Bullish)</option>
              <option value="blue">🔵 Blue (Bearish)</option>
              <option value="gray">⚪ Gray (Neutral)</option>
            </select>
          </div>
        </div>

        <div className="filter-drawer-footer">
          <div className="filter-results-mobile">
            Showing {filteredCount} of {totalCount} cryptocurrencies
          </div>

          <div className="filter-drawer-actions">
            {hasActiveFilters && (
              <button onClick={onClearFilters} className="filter-drawer-clear">
                Clear All
              </button>
            )}
            <button onClick={onClose} className="filter-drawer-apply">
              Apply Filters
            </button>
          </div>
        </div>
      </div>
    </>
  );
};
