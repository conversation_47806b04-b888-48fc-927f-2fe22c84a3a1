{"name": "crypto-indicator-web", "version": "0.1.0", "private": true, "proxy": "http://localhost:6701", "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "darkreader": "^4.9.105", "dotenv": "^16.5.0", "lightweight-charts": "^5.0.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "devDependencies": {"@antfu/eslint-config": "^4.15.0", "@types/node": "^18.19.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.29.0", "prettier": "^3.1.1", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "generate-client": "node scripts/generate-client.js", "generate-client:dev": "npm run generate-client && echo 'Client generated successfully!'", "generate-client:build": "npm run generate-client", "lint": "eslint src --ext .ts,.tsx --fix", "lint:check": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}